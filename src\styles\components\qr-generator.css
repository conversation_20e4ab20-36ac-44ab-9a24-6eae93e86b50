/* QR Code Generator Component Styles */
.qr-generator-container {
  width: 100%;
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
  overflow: hidden;
}

.qr-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.qr-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.qr-title i {
  color: #007bff;
}

.qr-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  background: none;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.5rem;
  cursor: pointer;
  color: #6c757d;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.btn-icon:hover {
  background: #e9ecef;
  color: #495057;
  border-color: #adb5bd;
}

.btn-refresh:hover {
  color: #007bff;
  border-color: #007bff;
}

.btn-download:hover {
  color: #28a745;
  border-color: #28a745;
}

.qr-content {
  padding: 1.5rem;
}

.qr-display {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 220px;
  margin-bottom: 1.5rem;
  position: relative;
  background: #fafafa;
  border-radius: 8px;
  border: 2px dashed #e1e5e9;
}

.qr-placeholder {
  text-align: center;
  color: #6c757d;
}

.qr-placeholder i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #adb5bd;
}

.qr-placeholder p {
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.qr-placeholder small {
  color: #868e96;
}

.qr-canvas,
.qr-svg {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: white;
  padding: 0.5rem;
}

.qr-error {
  text-align: center;
  color: #dc3545;
  padding: 2rem;
}

.qr-error i {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.qr-error p {
  margin: 0;
  font-weight: 500;
}

.qr-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-size: 0.85rem;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

.qr-data {
  font-size: 0.8rem;
  color: #6c757d;
  background: #f8f9fa;
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  word-break: break-all;
  font-family: 'Courier New', monospace;
}

.qr-size {
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 500;
}

.qr-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  color: #007bff;
  font-weight: 500;
  z-index: 10;
}

.qr-loading i {
  font-size: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .qr-header {
    padding: 0.75rem;
  }
  
  .qr-title {
    font-size: 0.9rem;
  }
  
  .qr-content {
    padding: 1rem;
  }
  
  .qr-display {
    min-height: 180px;
    margin-bottom: 1rem;
  }
  
  .qr-placeholder i {
    font-size: 2.5rem;
  }
  
  .btn-icon {
    width: 32px;
    height: 32px;
    padding: 0.375rem;
  }
}

/* Animation for QR code appearance */
.qr-canvas,
.qr-svg {
  animation: qrFadeIn 0.5s ease-out;
}

@keyframes qrFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading animation */
.qr-loading i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Success state */
.qr-generator-container.success .qr-display {
  border-color: #28a745;
  background: #f8fff9;
}

.qr-generator-container.success .qr-title i {
  color: #28a745;
}

/* Error state */
.qr-generator-container.error .qr-display {
  border-color: #dc3545;
  background: #fff8f8;
}

.qr-generator-container.error .qr-title i {
  color: #dc3545;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .qr-generator-container {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .qr-header {
    background: #1a202c;
    border-color: #2d3748;
  }
  
  .qr-title {
    color: #e2e8f0;
  }
  
  .qr-display {
    background: #1a202c;
    border-color: #4a5568;
  }
  
  .qr-placeholder {
    color: #a0aec0;
  }
  
  .qr-data {
    background: #1a202c;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .btn-icon {
    border-color: #4a5568;
    color: #a0aec0;
  }
  
  .btn-icon:hover {
    background: #2d3748;
    color: #e2e8f0;
  }
}

/* Print styles */
@media print {
  .qr-header {
    background: white !important;
    border-bottom: 1px solid #000;
  }
  
  .qr-actions {
    display: none;
  }
  
  .qr-display {
    border: 1px solid #000;
    background: white;
  }
  
  .qr-canvas,
  .qr-svg {
    box-shadow: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .qr-generator-container {
    border-width: 2px;
  }
  
  .qr-display {
    border-width: 3px;
  }
  
  .btn-icon {
    border-width: 2px;
  }
}

/* Focus styles for accessibility */
.btn-icon:focus-visible {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Tooltip for buttons */
.btn-icon {
  position: relative;
}

.btn-icon::after {
  content: attr(title);
  position: absolute;
  bottom: -2.5rem;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1000;
}

.btn-icon:hover::after {
  opacity: 1;
}
