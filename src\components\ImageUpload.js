/**
 * Reusable Image Upload Component
 * Handles file selection, preview, compression, and WebP conversion
 */
export class ImageUpload {
  constructor(options = {}) {
    this.options = {
      multiple: false,
      maxFiles: 1,
      maxSize: 10 * 1024 * 1024, // 10MB
      quality: 0.85,
      maxWidth: 800,
      maxHeight: 800,
      acceptedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      ...options
    };
    
    this.files = [];
    this.onFilesChange = options.onFilesChange || (() => {});
    this.onError = options.onError || ((error) => console.error(error));
  }

  /**
   * Create the HTML structure for the image upload component
   */
  createHTML(containerId) {
    return `
      <div class="image-upload-container" id="${containerId}">
        <div class="image-upload-area" id="${containerId}-upload-area">
          <input 
            type="file" 
            id="${containerId}-input" 
            class="image-upload-input" 
            accept="image/*" 
            ${this.options.multiple ? 'multiple' : ''}
            style="display: none;"
          >
          <div class="upload-placeholder" id="${containerId}-placeholder">
            <div class="upload-icon">
              <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <div class="upload-text">
              <p class="upload-title">
                ${this.options.multiple ? 'Upload Images' : 'Upload Image'}
              </p>
              <p class="upload-subtitle">
                Click to browse or drag and drop
              </p>
              <p class="upload-info">
                ${this.options.multiple ? `Max ${this.options.maxFiles} files` : 'Single file'} • 
                Max ${Math.round(this.options.maxSize / (1024 * 1024))}MB • 
                JPG, PNG, WebP, GIF
              </p>
            </div>
          </div>
          <div class="image-previews" id="${containerId}-previews" style="display: none;">
            <!-- Image previews will be inserted here -->
          </div>
        </div>
        <div class="upload-progress" id="${containerId}-progress" style="display: none;">
          <div class="progress-bar">
            <div class="progress-fill" id="${containerId}-progress-fill"></div>
          </div>
          <div class="progress-text" id="${containerId}-progress-text">Processing...</div>
        </div>
      </div>
    `;
  }

  /**
   * Initialize the component after HTML is inserted
   */
  initialize(containerId) {
    this.containerId = containerId;
    this.container = document.getElementById(containerId);
    this.input = document.getElementById(`${containerId}-input`);
    this.uploadArea = document.getElementById(`${containerId}-upload-area`);
    this.placeholder = document.getElementById(`${containerId}-placeholder`);
    this.previews = document.getElementById(`${containerId}-previews`);
    this.progress = document.getElementById(`${containerId}-progress`);
    this.progressFill = document.getElementById(`${containerId}-progress-fill`);
    this.progressText = document.getElementById(`${containerId}-progress-text`);

    this.bindEvents();
  }

  /**
   * Bind event listeners
   */
  bindEvents() {
    // Click to upload
    this.uploadArea.addEventListener('click', (e) => {
      if (e.target.closest('.remove-image')) return;
      this.input.click();
    });

    // File input change
    this.input.addEventListener('change', (e) => {
      this.handleFiles(Array.from(e.target.files));
    });

    // Drag and drop
    this.uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      this.uploadArea.classList.add('drag-over');
    });

    this.uploadArea.addEventListener('dragleave', (e) => {
      e.preventDefault();
      this.uploadArea.classList.remove('drag-over');
    });

    this.uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      this.uploadArea.classList.remove('drag-over');
      const files = Array.from(e.dataTransfer.files).filter(file => 
        file.type.startsWith('image/')
      );
      this.handleFiles(files);
    });
  }

  /**
   * Handle selected files
   */
  async handleFiles(fileList) {
    if (!fileList.length) return;

    // Validate file count
    const totalFiles = this.files.length + fileList.length;
    if (totalFiles > this.options.maxFiles) {
      this.onError(`Maximum ${this.options.maxFiles} files allowed`);
      return;
    }

    this.showProgress();

    try {
      const processedFiles = [];
      
      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i];
        
        // Update progress
        const progress = ((i + 1) / fileList.length) * 100;
        this.updateProgress(progress, `Processing ${file.name}...`);

        // Validate file
        if (!this.validateFile(file)) continue;

        // Process file
        const processedFile = await this.processFile(file);
        if (processedFile) {
          processedFiles.push(processedFile);
        }
      }

      // Add processed files
      this.files.push(...processedFiles);
      this.updatePreviews();
      this.onFilesChange(this.files);

    } catch (error) {
      this.onError(`Error processing files: ${error.message}`);
    } finally {
      this.hideProgress();
    }
  }

  /**
   * Validate individual file
   */
  validateFile(file) {
    // Check file type
    if (!this.options.acceptedTypes.includes(file.type)) {
      this.onError(`${file.name}: Unsupported file type`);
      return false;
    }

    // Check file size
    if (file.size > this.options.maxSize) {
      this.onError(`${file.name}: File too large (max ${Math.round(this.options.maxSize / (1024 * 1024))}MB)`);
      return false;
    }

    return true;
  }

  /**
   * Process file: compress and convert to WebP
   */
  async processFile(file) {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        try {
          // Calculate new dimensions
          let { width, height } = this.calculateDimensions(img.width, img.height);
          
          canvas.width = width;
          canvas.height = height;

          // Draw and compress
          ctx.drawImage(img, 0, 0, width, height);
          
          canvas.toBlob((blob) => {
            if (blob) {
              const processedFile = new File([blob], 
                file.name.replace(/\.[^/.]+$/, '.webp'), 
                { type: 'image/webp' }
              );
              
              // Create preview URL
              const previewUrl = URL.createObjectURL(blob);
              
              resolve({
                file: processedFile,
                originalFile: file,
                previewUrl: previewUrl,
                name: file.name,
                size: blob.size
              });
            } else {
              reject(new Error('Failed to compress image'));
            }
          }, 'image/webp', this.options.quality);

        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Calculate new dimensions maintaining aspect ratio
   */
  calculateDimensions(originalWidth, originalHeight) {
    const { maxWidth, maxHeight } = this.options;
    
    if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
      return { width: originalWidth, height: originalHeight };
    }

    const aspectRatio = originalWidth / originalHeight;
    
    if (originalWidth > originalHeight) {
      return {
        width: Math.min(maxWidth, originalWidth),
        height: Math.min(maxWidth / aspectRatio, originalHeight)
      };
    } else {
      return {
        width: Math.min(maxHeight * aspectRatio, originalWidth),
        height: Math.min(maxHeight, originalHeight)
      };
    }
  }

  /**
   * Update image previews
   */
  updatePreviews() {
    if (this.files.length === 0) {
      this.placeholder.style.display = 'block';
      this.previews.style.display = 'none';
      return;
    }

    this.placeholder.style.display = 'none';
    this.previews.style.display = 'block';

    const previewsHTML = this.files.map((fileData, index) => `
      <div class="image-preview" data-index="${index}">
        <div class="preview-image">
          <img src="${fileData.previewUrl}" alt="${fileData.name}">
          <button type="button" class="remove-image" data-index="${index}">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="preview-info">
          <div class="preview-name">${fileData.name}</div>
          <div class="preview-size">${this.formatFileSize(fileData.size)}</div>
        </div>
      </div>
    `).join('');

    this.previews.innerHTML = previewsHTML;

    // Bind remove buttons
    this.previews.querySelectorAll('.remove-image').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const index = parseInt(btn.dataset.index);
        this.removeFile(index);
      });
    });
  }

  /**
   * Remove file by index
   */
  removeFile(index) {
    if (this.files[index]) {
      // Revoke object URL to free memory
      URL.revokeObjectURL(this.files[index].previewUrl);
      this.files.splice(index, 1);
      this.updatePreviews();
      this.onFilesChange(this.files);
    }
  }

  /**
   * Show progress indicator
   */
  showProgress() {
    this.progress.style.display = 'block';
  }

  /**
   * Hide progress indicator
   */
  hideProgress() {
    this.progress.style.display = 'none';
  }

  /**
   * Update progress
   */
  updateProgress(percent, text) {
    this.progressFill.style.width = `${percent}%`;
    this.progressText.textContent = text;
  }

  /**
   * Format file size
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get processed files
   */
  getFiles() {
    return this.files.map(fileData => fileData.file);
  }

  /**
   * Clear all files
   */
  clear() {
    this.files.forEach(fileData => {
      URL.revokeObjectURL(fileData.previewUrl);
    });
    this.files = [];
    this.updatePreviews();
    this.onFilesChange(this.files);
  }

  /**
   * Set existing files (for edit mode)
   */
  setExistingFiles(urls) {
    this.files = urls.map((url, index) => ({
      previewUrl: url,
      name: `existing-${index}.webp`,
      size: 0,
      isExisting: true,
      url: url
    }));
    this.updatePreviews();
  }
}
