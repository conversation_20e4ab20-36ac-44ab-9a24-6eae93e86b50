/* Price Formatter Component Styles */
.price-formatter-container {
  width: 100%;
}

.price-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.currency-prefix {
  position: absolute;
  left: 1rem;
  color: #495057;
  font-weight: 500;
  font-size: 0.9rem;
  z-index: 1;
  pointer-events: none;
}

.price-input {
  padding-left: 3rem !important;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-weight: 500;
  font-size: 0.9rem;
  text-align: left;
}

.price-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.price-input::placeholder {
  color: #adb5bd;
  font-weight: normal;
}

.price-info {
  margin-top: 0.5rem;
}

.price-info small {
  font-size: 0.8rem;
}

/* Error State */
.price-formatter-container.error .price-input {
  border-color: #dc3545;
}

.price-formatter-container.error .price-input:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

.price-formatter-container.error .currency-prefix {
  color: #dc3545;
}

/* Disabled State */
.price-formatter-container.disabled .price-input {
  background-color: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.6;
}

.price-formatter-container.disabled .currency-prefix {
  color: #6c757d;
  opacity: 0.6;
}

/* Success State */
.price-formatter-container.success .price-input {
  border-color: #28a745;
}

.price-formatter-container.success .price-input:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
}

.price-formatter-container.success .currency-prefix {
  color: #28a745;
}

/* Large Size Variant */
.price-formatter-container.large .price-input {
  padding: 1rem 1rem 1rem 3.5rem;
  font-size: 1.1rem;
  min-height: 56px;
}

.price-formatter-container.large .currency-prefix {
  left: 1.25rem;
  font-size: 1.1rem;
}

/* Small Size Variant */
.price-formatter-container.small .price-input {
  padding: 0.5rem 0.75rem 0.5rem 2.5rem;
  font-size: 0.85rem;
  min-height: 36px;
}

.price-formatter-container.small .currency-prefix {
  left: 0.75rem;
  font-size: 0.85rem;
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .price-input {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .price-input:focus {
    border-color: #63b3ed;
    box-shadow: 0 0 0 2px rgba(99, 179, 237, 0.25);
  }
  
  .currency-prefix {
    color: #e2e8f0;
  }
  
  .price-formatter-container.disabled .price-input {
    background-color: #1a202c;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .price-input {
    border-width: 2px;
  }
  
  .currency-prefix {
    font-weight: 600;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .price-input {
    font-size: 16px; /* Prevent zoom on iOS */
    padding-left: 2.75rem !important;
  }
  
  .currency-prefix {
    left: 0.875rem;
    font-size: 0.85rem;
  }
}

/* Animation for value changes */
.price-input {
  transition: all 0.2s ease;
}

.price-formatter-container.value-changed .price-input {
  background-color: #e8f5e8;
  animation: valueHighlight 0.6s ease;
}

@keyframes valueHighlight {
  0% {
    background-color: #e8f5e8;
  }
  100% {
    background-color: white;
  }
}

/* Focus ring for accessibility */
.price-input:focus-visible {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Custom number input styling */
.price-input::-webkit-outer-spin-button,
.price-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.price-input[type=number] {
  -moz-appearance: textfield;
}

/* Validation states */
.price-formatter-container .validation-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.9rem;
}

.price-formatter-container.error .validation-icon {
  color: #dc3545;
}

.price-formatter-container.success .validation-icon {
  color: #28a745;
}

/* Loading state */
.price-formatter-container.loading .price-input {
  background-image: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Tooltip for formatting info */
.price-formatter-container .format-tooltip {
  position: absolute;
  top: -2.5rem;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1000;
}

.price-formatter-container .format-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: #333;
}

.price-formatter-container:hover .format-tooltip {
  opacity: 1;
}
